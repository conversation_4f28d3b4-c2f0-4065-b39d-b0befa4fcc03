# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
DEPLOYMENT.md
SUPABASE_SETUP.md
INTEGRATION_SUMMARY.md
DEPLOYMENT_CHECKLIST.md
TWITTER_OAUTH_SETUP.md
KOYEB_DEPLOYMENT_GUIDE.md
KOYEB_DEPLOYMENT_GUIDE.md
KOYEB_DEPLOYMENT_GUIDE.md
KOYEB_DEPLOYMENT_GUIDE.md
COMMUNITY_VALIDATION_GUIDE.md
BEFORE_AFTER_COMPARISON.md
LAYEREDGE_DESIGN_UPDATE_SUMMARY.md
test-layeredge-styles.html
verify-fix.md
REAL_TIME_ENGAGEMENT_IMPLEMENTATION.md
PRISMA_BUILD_FIX_SUMMARY.md
FALLBACK_SYSTEM_IMPLEMENTATION.md
TESTING_GUIDE.md
